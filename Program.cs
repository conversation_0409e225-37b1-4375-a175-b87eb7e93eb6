// using ModelContextProtocol.Client;
// using Microsoft.SemanticKernel;

// var kernerl = Kernel.CreateBuilder();

// #pragma warning disable SKEXP0070       
// kernerl.Services.AddOllamaChatCompletion(
//         "qwen3:32b",
//         //"llama3.3:latest",
//         new Uri("http://************:11434")
//     );
// #pragma warning restore SKEXP0070

// Kernel kernel = kernerl.Build();

// var transport = new SseClientTransport(
//     new SseClientTransportOptions()
//     {
//         ConnectionTimeout = TimeSpan.FromSeconds(500),
//         Name = "playwright",
//         TransportMode = HttpTransportMode.Sse,
//         Endpoint = new Uri("http://localhost:8931")
//     });
// await using IMcpClient mcp = await McpClientFactory.CreateAsync(transport);

// var tools = await mcp.ListToolsAsync();

// #pragma warning disable SKEXP0001                  
// kernel.Plugins.AddFromFunctions(
//     "Playwright",                                        
//     tools.Select(t => t.AsKernelFunction()));         
// #pragma warning restore SKEXP0001

// var settings = new PromptExecutionSettings
// {
//     FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(),

// };

// const string ask =
//     "You are a helpful browser automation agent. You can use the tools provided to interact with web pages." +
//     "You can navigate to pages, click buttons, fill forms, and extract information from your tools. " +
//     "You will be given a URL to visit, and you should use the tools to interact with the page as needed. " +
//     "You should not include any data in your response that you did not extract from the page. " +
//     "After you go to the page, you can extarct information from the page using the tools provided." +
//     "Go to the link: 'https://www.letgo.com/televizyon_c15112?city_id=4000040&filter=tur:led-lcd,qled,oled;ekran-boyutu:55-inc-139-cm,55-inc-140-cm,58-inc-147-cm,65-inc-163-cm,65-inc-164-cm,65-inc-165-cm;marka:samsung,lg,sony,philips&sorting=desc-creation' and get me the prices of top 10 listings.";

// var answer = await kernel.InvokePromptAsync(ask, new(settings));
// Console.WriteLine($"\n{answer}");


// Program.cs  (net8.0 / SemanticKernel ≥ 1.3.x)

//second


// using Microsoft.SemanticKernel;
// using ModelContextProtocol.Client;

// internal class Program
// {
//     private static async Task Main()
//     {
//         /******** 1 – Kernel + Ollama ********/
//         var kernelBuilder = Kernel.CreateBuilder();

// #pragma warning disable SKEXP0070
//         kernelBuilder.Services.AddOllamaChatCompletion(
//             modelId: "qwen3:32b",
//             endpoint: new Uri("http://************:11434"));
// #pragma warning restore SKEXP0070

//         Kernel kernel = kernelBuilder.Build();

//         /******** 2 – Playwright MCP transport & tool injection ********/
//         var transport = new SseClientTransport(new SseClientTransportOptions
//         {
//             Name = "playwright",
//             Endpoint = new Uri("http://localhost:8931"),
//             ConnectionTimeout = TimeSpan.FromSeconds(500),
//             TransportMode = HttpTransportMode.Sse
//         });

//         await using IMcpClient mcp = await McpClientFactory.CreateAsync(transport);

//         var tools = await mcp.ListToolsAsync();
// #pragma warning disable SKEXP0001
//         kernel.Plugins.AddFromFunctions(
//             "Playwright",
//             tools.Select(t => t.AsKernelFunction()));
// #pragma warning restore SKEXP0001

//         var execSettings = new PromptExecutionSettings
//         {
//             FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
//         };

//         /******** 3 – Chat loop ********/
//         Console.WriteLine("=== Playwright MCP Chat ===");
//         Console.WriteLine("Type 'exit' to quit.\n");

//         const string systemPrompt =
//             "You are a helpful browser-automation agent. "
//           + "Only report data you actually extracted with the provided tools.\n\n";

//         while (true)
//         {
//             Console.ForegroundColor = ConsoleColor.Cyan;
//             Console.Write("You ➜ ");
//             Console.ResetColor();
//             var userInput = Console.ReadLine();

//             if (string.Equals(userInput, "exit", StringComparison.OrdinalIgnoreCase))
//                 break;
//             if (string.IsNullOrWhiteSpace(userInput))
//                 continue;
//             try
//             {
//                 string prompt = systemPrompt + userInput;

//                 await foreach (var chunk in kernel.InvokePromptStreamingAsync(
//                                    prompt,
//                                    new(execSettings)))
//                 {
//                     if (chunk is StreamingTextContent text)
//                     {
//                         Console.Write(text.Text);
//                     }
//                 }
//                 Console.WriteLine();
//             }
//             catch (Exception ex)
//             {
//                 Console.ForegroundColor = ConsoleColor.Red;
//                 Console.WriteLine($"\n[Error] {ex.Message}");
//                 Console.ResetColor();
//             }
//         }
//     }
// }

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using ModelContextProtocol.Client;
internal class Program
{
    private const int HistoryLimit = 8;   // keep the last N turns

    private static async Task Main()
    {
        /******** 0 –  graceful Ctrl-C ********/
        using var cts = new CancellationTokenSource();
        Console.CancelKeyPress += (_, e) =>
        {
            e.Cancel = true;
            cts.Cancel();   // propagate cancellation
        };

        /******** 1 –  Kernel + logging + Ollama ********/
        var builder = Kernel.CreateBuilder();
        builder.Services.AddLogging(lb => lb.AddSimpleConsole());

#pragma warning disable SKEXP0070
        builder.Services.AddOllamaChatCompletion(
            modelId: "qwen3:32b",
            endpoint: new Uri("http://************:11434"));
#pragma warning restore SKEXP0070

        Kernel kernel = builder.Build();
        var log = kernel.GetRequiredService<ILogger<Program>>();
        var chatCompletionService = kernel.GetRequiredService<IChatCompletionService>();
        log.LogInformation("Kernel initialised.");

        /******** 2 –  Playwright MCP transport + tool injection ********/
        var transport = new SseClientTransport(new SseClientTransportOptions
        {
            Name              = "playwright",
            Endpoint          = new Uri("http://localhost:8931"),
            ConnectionTimeout = TimeSpan.FromSeconds(500),
            TransportMode     = HttpTransportMode.Sse
        });

        await using IMcpClient mcp = await McpClientFactory.CreateAsync(transport, cancellationToken: cts.Token);
        var tools = await mcp.ListToolsAsync(cancellationToken: cts.Token);

#pragma warning disable SKEXP0001
        kernel.Plugins.AddFromFunctions("Playwright", tools.Select(t => t.AsKernelFunction()));
#pragma warning restore SKEXP0001

        var execSettings = new PromptExecutionSettings
        {
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
        };

        /******** 3 –  chat state ********/
        var history = new ChatHistory();
        const string systemPrompt =
            "You are a helpful browser-automation agent. "
          + "Only report data you have actually extracted with the provided tools.\n";

        history.AddSystemMessage(systemPrompt);

        Console.WriteLine("=== Playwright MCP Chat (Ctrl-C to quit) ===\n");

        /******** 4 –  main loop ********/
        while (!cts.IsCancellationRequested)
        {
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.Write("You ➜ ");
            Console.ResetColor();

            string? userInput = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(userInput)) continue;

            history.AddUserMessage(userInput);
            TrimHistory(history);

            try
            {
                await foreach (var chunk in chatCompletionService.GetStreamingChatMessageContentsAsync(
                                   history,
                                   execSettings,
                                   kernel,
                                   cts.Token))
                {
                    switch (chunk)
                    {
                        default:
                            Console.ResetColor();
                            Console.Write(chunk.Content);
                            break;
                    }
                }

                Console.ResetColor();
                Console.WriteLine();

                history.AddAssistantMessage("(see above)");
                TrimHistory(history);
            }
            catch (OperationCanceledException)
            {
                log.LogInformation("Cancellation requested – shutting down.");
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Turn failed.");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"[Error] {ex.Message}");
                Console.ResetColor();
            }
        }

        log.LogInformation("Finished – goodbye!");
    }

    private static void TrimHistory(ChatHistory history)
    {
        if (history.Count > HistoryLimit)
        {
            var systemMessage = history.FirstOrDefault(m => m.Role == AuthorRole.System);
            var messagesToKeep = history.Skip(history.Count - HistoryLimit + (systemMessage != null ? 1 : 0)).ToList();

            history.Clear();
            if (systemMessage != null)
                history.Add(systemMessage);

            foreach (var message in messagesToKeep.Where(m => m.Role != AuthorRole.System))
                history.Add(message);
        }
    }
}