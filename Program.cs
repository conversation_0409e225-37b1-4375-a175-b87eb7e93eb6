using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using ModelContextProtocol.Client;
using System.Text;
internal class Program
{
    private const int HistoryLimit = 8;   
    private static readonly string[] ThinkingTags = { "<think>", "</think>" };

    private static async Task Main()
    {
        /******** 0 –  graceful Ctrl-C ********/
        using var cts = new CancellationTokenSource();
        Console.CancelKeyPress += (_, e) =>
        {
            e.Cancel = true;
            cts.Cancel();   // propagate cancellation
        };

        /******** 1 –  Kernel + logging + Ollama ********/
        var builder = Kernel.CreateBuilder();
        builder.Services.AddLogging(lb => lb.AddSimpleConsole());

#pragma warning disable SKEXP0070
        builder.Services.AddOllamaChatCompletion(
            modelId: "qwen3:latest",
            endpoint: new Uri("http://192.168.1.86:11434"));
#pragma warning restore SKEXP0070

        Kernel kernel = builder.Build();
        var log = kernel.GetRequiredService<ILogger<Program>>();
        var chatCompletionService = kernel.GetRequiredService<IChatCompletionService>();
        log.LogInformation("Kernel initialised.");

        /******** 2 –  Playwright MCP transport + tool injection ********/
        var transport = new SseClientTransport(new SseClientTransportOptions
        {
            Name              = "playwright",
            Endpoint          = new Uri("http://localhost:8931"),
            ConnectionTimeout = TimeSpan.FromSeconds(500),
            TransportMode     = HttpTransportMode.Sse
        });

        IMcpClient? mcp = null;
        try
        {
            log.LogInformation("Connecting to Playwright MCP server...");
            mcp = await McpClientFactory.CreateAsync(transport, cancellationToken: cts.Token);
            log.LogInformation("✅ Connected to Playwright MCP server successfully");

            log.LogInformation("Listing available tools...");
            var tools = await mcp.ListToolsAsync(cancellationToken: cts.Token);
            log.LogInformation("✅ Found {ToolCount} tools available", tools.Count);

            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine($"\n🔧 Available Playwright Tools ({tools.Count}):");
            Console.ResetColor();
            foreach (var tool in tools)
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"  • {tool.Name}");
                Console.ResetColor();
                if (!string.IsNullOrEmpty(tool.Description))
                {
                    Console.ForegroundColor = ConsoleColor.DarkGray;
                    Console.WriteLine($"    {tool.Description}");
                    Console.ResetColor();
                }
            }
            Console.WriteLine();

#pragma warning disable SKEXP0001
            kernel.Plugins.AddFromFunctions("Playwright", tools.Select(t => t.AsKernelFunction()));
#pragma warning restore SKEXP0001
            log.LogInformation("✅ Tools registered with Semantic Kernel");
        }
        catch (Exception ex)
        {
            log.LogError(ex, "❌ Failed to connect to Playwright MCP server");
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"❌ MCP Connection Error: {ex.Message}");
            Console.ResetColor();

            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("\n🔧 Troubleshooting Steps:");
            Console.WriteLine("1. Make sure the Playwright MCP server is running on http://localhost:8931");
            Console.WriteLine("2. Check if the server is accessible:");
            Console.WriteLine("   curl http://localhost:8931/health (if available)");
            Console.WriteLine("3. Verify the MCP server configuration");
            Console.WriteLine("4. Check firewall settings");
            Console.ResetColor();

            Console.WriteLine("\n⚠️  Cannot proceed without MCP connection. Exiting...");
            return;
        }

        var execSettings = new PromptExecutionSettings
        {
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
        };

        /******** 3 –  chat state ********/
        var history = new ChatHistory();
        const string systemPrompt = @"You are an expert browser automation agent using Playwright tools via MCP (Model Context Protocol).

🎯 YOUR MISSION:
- Automate web browser tasks step-by-step
- Navigate websites, interact with elements, extract data
- Always use the provided Playwright tools to accomplish tasks
- Be methodical and thorough in your approach

🔧 AVAILABLE TOOLS:
You have access to Playwright browser automation tools. Use them to:
- Navigate to URLs
- Find and interact with page elements (click, type, etc.)
- Extract text, data, and information from pages
- Take screenshots when helpful
- Handle forms, buttons, links, and other web elements

📋 STEP-BY-STEP APPROACH:
1. **Start with navigation**: Always begin by navigating to the target URL
2. **Wait for page load**: Ensure pages are fully loaded before proceeding
3. **Locate elements**: Find the specific elements you need to interact with
4. **Perform actions**: Click, type, or extract data as needed
5. **Verify results**: Check that actions were successful
6. **Continue systematically**: Work through tasks methodically

⚠️ IMPORTANT RULES:
- ALWAYS use the provided tools - never assume or make up information
- DO NOT try to install browsers or dependencies - assume they are already set up
- If a tool fails, try alternative approaches or report the specific error
- Be explicit about what you're doing at each step
- Only report data you actually extracted using the tools
- If you encounter errors, explain what went wrong and suggest solutions

🎨 COMMUNICATION:
- Use <think> tags to show your reasoning process
- Explain each step you're taking
- Report both successes and failures clearly
- Provide helpful context about what you observe on pages

Ready to help with browser automation tasks!";

        history.AddSystemMessage(systemPrompt);

        Console.ForegroundColor = ConsoleColor.Green;
        Console.WriteLine("🎭 === Playwright MCP Browser Automation Agent ===");
        Console.ResetColor();
        Console.WriteLine("Ready to help with browser automation tasks!");
        Console.WriteLine("💡 Try commands like:");
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine("   • 'Navigate to https://example.com'");
        Console.WriteLine("   • 'Click the login button'");
        Console.WriteLine("   • 'Extract all links from the page'");
        Console.WriteLine("   • 'Fill out the contact form'");
        Console.ResetColor();
        Console.WriteLine("Press Ctrl-C to quit\n");

        /******** 4 –  main loop ********/
        while (!cts.IsCancellationRequested)
        {
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.Write("You ➜ ");
            Console.ResetColor();

            string? userInput = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(userInput)) continue;

            history.AddUserMessage(userInput);
            TrimHistory(history);

            try
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("🤖 Assistant is thinking...");
                Console.ResetColor();

                var responseBuilder = new StringBuilder();
                var currentHistoryCount = history.Count;

                await foreach (var chunk in chatCompletionService.GetStreamingChatMessageContentsAsync(
                                   history,
                                   execSettings,
                                   kernel,
                                   cts.Token))
                {
                    if (!string.IsNullOrEmpty(chunk.Content))
                    {
                        responseBuilder.Append(chunk.Content);
                        WriteColoredContent(chunk.Content);
                    }
                }

                Console.ResetColor();
                Console.WriteLine();

                // Check if any function calls were made during this turn
                if (history.Count > currentHistoryCount)
                {
                    Console.ForegroundColor = ConsoleColor.Magenta;
                    Console.WriteLine($"\n🔧 Function calls detected ({history.Count - currentHistoryCount} new messages):");
                    Console.ResetColor();

                    for (int i = currentHistoryCount; i < history.Count; i++)
                    {
                        var message = history[i];
                        if (message.Role == AuthorRole.Assistant)
                        {
                            // Check for function calls in assistant message
                            foreach (var item in message.Items ?? [])
                            {
                                if (item is FunctionCallContent functionCall)
                                {
                                    Console.ForegroundColor = ConsoleColor.Blue;
                                    Console.WriteLine($"  📞 Calling: {functionCall.FunctionName}");
                                    Console.ForegroundColor = ConsoleColor.DarkBlue;
                                    Console.WriteLine($"     Args: {functionCall.Arguments}");
                                    Console.ResetColor();
                                }
                            }
                        }
                        else if (message.Role == AuthorRole.Tool)
                        {
                            // Check for function results
                            foreach (var item in message.Items ?? [])
                            {
                                if (item is FunctionResultContent functionResult)
                                {
                                    Console.ForegroundColor = ConsoleColor.Green;
                                    Console.WriteLine($"  ✅ Result from {functionResult.FunctionName}:");
                                    Console.ForegroundColor = ConsoleColor.DarkGreen;
                                    var result = functionResult.Result?.ToString() ?? "null";
                                    if (result.Length > 200)
                                        result = result[..200] + "...";
                                    Console.WriteLine($"     {result}");
                                    Console.ResetColor();
                                }
                            }
                        }
                    }
                    Console.WriteLine();
                }

                history.AddAssistantMessage("(see above)");
                TrimHistory(history);
            }
            catch (OperationCanceledException)
            {
                log.LogInformation("Cancellation requested – shutting down.");
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Turn failed.");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"[Error] {ex.Message}");
                Console.ResetColor();
            }
        }

        log.LogInformation("Finished – goodbye!");

        // Dispose MCP client
        if (mcp != null)
        {
            await mcp.DisposeAsync();
            log.LogInformation("MCP client disposed");
        }
    }

    private static void TrimHistory(ChatHistory history)
    {
        if (history.Count > HistoryLimit)
        {
            var systemMessage = history.FirstOrDefault(m => m.Role == AuthorRole.System);
            var messagesToKeep = history.Skip(history.Count - HistoryLimit + (systemMessage != null ? 1 : 0)).ToList();

            history.Clear();
            if (systemMessage != null)
                history.Add(systemMessage);

            foreach (var message in messagesToKeep.Where(m => m.Role != AuthorRole.System))
                history.Add(message);
        }
    }

    private static void WriteColoredContent(string content)
    {
        if (string.IsNullOrEmpty(content)) return;

        if (content.Contains("<think>") || content.Contains("</think>"))
        {
            var parts = content.Split(ThinkingTags, StringSplitOptions.None);

            for (int i = 0; i < parts.Length; i++)
            {
                if (i % 2 == 0)
                {
                    // Regular content (outside thinking tags)
                    Console.ResetColor();
                    Console.Write(parts[i]);
                }
                else
                {
                    // Thinking content (inside thinking tags) - show in different color
                    Console.ForegroundColor = ConsoleColor.DarkCyan;
                    Console.Write("💭 ");
                    Console.ForegroundColor = ConsoleColor.Cyan;
                    Console.Write(parts[i]);
                    Console.ResetColor();
                }
            }
        }
        else
        {
            // No thinking tags, just write normally
            Console.ResetColor();
            Console.Write(content);
        }
    }
}