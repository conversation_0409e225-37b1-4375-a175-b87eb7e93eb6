using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using ModelContextProtocol.Client;
using System.Text;
internal class Program
{
    private const int HistoryLimit = 8;   
    private static readonly string[] ThinkingTags = { "<think>", "</think>" };

    private static async Task Main()
    {
        /******** 0 –  graceful Ctrl-C ********/
        using var cts = new CancellationTokenSource();
        Console.CancelKeyPress += (_, e) =>
        {
            e.Cancel = true;
            cts.Cancel();   // propagate cancellation
        };

        /******** 1 –  Kernel + logging + Ollama ********/
        var builder = Kernel.CreateBuilder();
        builder.Services.AddLogging(lb => lb.AddSimpleConsole());

#pragma warning disable SKEXP0070
        builder.Services.AddOllamaChatCompletion(
            modelId: "qwen3:latest",
            endpoint: new Uri("http://192.168.1.86:11434"));
#pragma warning restore SKEXP0070

        Kernel kernel = builder.Build();
        var log = kernel.GetRequiredService<ILogger<Program>>();
        var chatCompletionService = kernel.GetRequiredService<IChatCompletionService>();
        log.LogInformation("Kernel initialised.");

        /******** 2 –  Playwright MCP transport + tool injection ********/
        var transport = new SseClientTransport(new SseClientTransportOptions
        {
            Name              = "playwright",
            Endpoint          = new Uri("http://localhost:8931"),
            ConnectionTimeout = TimeSpan.FromSeconds(500),
            TransportMode     = HttpTransportMode.Sse
        });

        await using IMcpClient mcp = await McpClientFactory.CreateAsync(transport, cancellationToken: cts.Token);
        var tools = await mcp.ListToolsAsync(cancellationToken: cts.Token);

#pragma warning disable SKEXP0001
        kernel.Plugins.AddFromFunctions("Playwright", tools.Select(t => t.AsKernelFunction()));
#pragma warning restore SKEXP0001

        var execSettings = new PromptExecutionSettings
        {
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
        };

        /******** 3 –  chat state ********/
        var history = new ChatHistory();
        const string systemPrompt =
            "You are a helpful browser-automation agent. "
          + "Only report data you have actually extracted with the provided tools.\n";

        history.AddSystemMessage(systemPrompt);

        Console.WriteLine("=== Playwright MCP Chat (Ctrl-C to quit) ===\n");

        /******** 4 –  main loop ********/
        while (!cts.IsCancellationRequested)
        {
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.Write("You ➜ ");
            Console.ResetColor();

            string? userInput = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(userInput)) continue;

            history.AddUserMessage(userInput);
            TrimHistory(history);

            try
            {
                var responseBuilder = new StringBuilder();
                await foreach (var chunk in chatCompletionService.GetStreamingChatMessageContentsAsync(
                                   history,
                                   execSettings,
                                   kernel,
                                   cts.Token))
                {
                    if (!string.IsNullOrEmpty(chunk.Content))
                    {
                        responseBuilder.Append(chunk.Content);
                        WriteColoredContent(chunk.Content);
                    }
                }

                Console.ResetColor();
                Console.WriteLine();

                history.AddAssistantMessage("(see above)");
                TrimHistory(history);
            }
            catch (OperationCanceledException)
            {
                log.LogInformation("Cancellation requested – shutting down.");
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Turn failed.");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"[Error] {ex.Message}");
                Console.ResetColor();
            }
        }

        log.LogInformation("Finished – goodbye!");
    }

    private static void TrimHistory(ChatHistory history)
    {
        if (history.Count > HistoryLimit)
        {
            var systemMessage = history.FirstOrDefault(m => m.Role == AuthorRole.System);
            var messagesToKeep = history.Skip(history.Count - HistoryLimit + (systemMessage != null ? 1 : 0)).ToList();

            history.Clear();
            if (systemMessage != null)
                history.Add(systemMessage);

            foreach (var message in messagesToKeep.Where(m => m.Role != AuthorRole.System))
                history.Add(message);
        }
    }

    private static void WriteColoredContent(string content)
    {
        if (string.IsNullOrEmpty(content)) return;

        if (content.Contains("<think>") || content.Contains("</think>"))
        {
            var parts = content.Split(ThinkingTags, StringSplitOptions.None);

            for (int i = 0; i < parts.Length; i++)
            {
                if (i % 2 == 0)
                {
                    Console.ForegroundColor = ConsoleColor.Cyan;
                    Console.ResetColor();
                    Console.Write(parts[i]);
                }
                else
                {
                    Console.ForegroundColor = ConsoleColor.DarkGray;
                    Console.Write($"[THINKING: {parts[i]}]");
                }
            }
        }
        else
        {
            Console.ResetColor();
            Console.Write(content);
        }
    }
}