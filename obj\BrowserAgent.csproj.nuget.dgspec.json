{"format": 1, "restore": {"C:\\Users\\<USER>\\BrowserAgent\\BrowserAgent.csproj": {}}, "projects": {"C:\\Users\\<USER>\\BrowserAgent\\BrowserAgent.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\BrowserAgent\\BrowserAgent.csproj", "projectName": "BrowserAgent", "projectPath": "C:\\Users\\<USER>\\BrowserAgent\\BrowserAgent.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\BrowserAgent\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"CommunityToolkit.Aspire.OllamaSharp": {"target": "Package", "version": "[9.5.1-beta.306, )"}, "Microsoft.Extensions.AI": {"target": "Package", "version": "[9.6.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.SemanticKernel.Connectors.Ollama": {"target": "Package", "version": "[1.56.0-alpha, )"}, "ModelContextProtocol": {"target": "Package", "version": "[0.2.0-preview.3, )"}, "ModelContextProtocol.Core": {"target": "Package", "version": "[0.2.0-preview.3, )"}, "OllamaSharp": {"target": "Package", "version": "[5.2.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}