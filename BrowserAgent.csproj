<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Aspire.OllamaSharp" Version="9.5.1-beta.306" />
    <PackageReference Include="Microsoft.Extensions.AI" Version="9.6.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Ollama" Version="1.56.0-alpha" />
    <PackageReference Include="ModelContextProtocol" Version="0.2.0-preview.3" />
    <PackageReference Include="ModelContextProtocol.Core" Version="0.2.0-preview.3" />
    <PackageReference Include="OllamaSharp" Version="5.2.2" />
  </ItemGroup>

</Project>
